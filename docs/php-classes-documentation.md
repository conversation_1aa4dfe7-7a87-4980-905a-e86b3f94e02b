# PHP Classes Documentation

This document provides comprehensive documentation for all PHP classes in the codebase, organized by functionality and namespace.

## Table of Contents

1. [System Classes](#system-classes)
2. [Edge Template System](#edge-template-system)
3. [Database Classes](#database-classes)
4. [Data Processing Classes](#data-processing-classes)
5. [Autodesk API Classes](#autodesk-api-classes)
6. [Notification System](#notification-system)

---

## System Classes

### router Class

**Namespace:** `system`  
**File:** `system/classes/router.class.php`

The router class handles application routing, URL processing, and view resolution.

#### Properties

```php
protected static tcs $tcs;           // TCS instance reference
public static array $routes;        // Route definitions array
```

#### Key Methods

##### `route()`
Main routing method that processes the current request and determines the appropriate response.

```php
public static function route(): void
```

**Features:**
- Handles special routes (scheduled, api, settings, login, logout)
- Processes view routes with HTMX support
- Manages authentication and permissions
- Supports both direct URL access and HTMX requests

##### `get_route($path)`
Resolves a path to a view file or API endpoint.

```php
public static function get_route(string $path): array
```

**Parameters:**
- `$path` - The route path to resolve

**Returns:** Array with status and resolved path information

**Usage Example:**
```php
$route_result = router::get_route('subscriptions/view');
if ($route_result['status'] === 'success') {
    $view_path = $route_result['path'];
}
```

#### Route Types

1. **View Routes** - Standard page views
2. **API Routes** - API endpoints for data processing
3. **Special Routes** - System routes (login, settings, etc.)

---

### users Class

**Namespace:** `system`  
**File:** `system/classes/users.class.php`

Handles user authentication, session management, and user operations.

#### Properties

```php
private static $db;              // Database connection
public static int $id;           // Current user ID
public static string $username;  // Current username
public static string $email;     // Current user email
public static string $role;      // Current user role
public static bool $started;     // Session started flag
```

#### Key Methods

##### `login($username, $password)`
Authenticates a user with username and password.

```php
public static function login(string $username, string $password): array|bool
```

**Parameters:**
- `$username` - User's username
- `$password` - User's password (plain text)

**Returns:** User data array on success, false on failure

**Features:**
- Password verification using `password_verify()`
- Session token generation
- Comprehensive logging
- Cookie-based "remember me" functionality

##### `checkAuth()`
Validates current user authentication status.

```php
public static function checkAuth(): array|false
```

**Returns:** User data if authenticated, false otherwise

**Features:**
- Token-based authentication
- Session and cookie validation
- Automatic session refresh

##### `logout()`
Logs out the current user and cleans up sessions.

```php
public static function logout(): bool
```

**Features:**
- Session cleanup
- Cookie removal
- Database session invalidation

##### `getUserById($user_id)`
Retrieves user details by ID.

```php
public static function getUserById(int $user_id): array|false
```

**Usage Example:**
```php
// Login user
$user = users::login('john_doe', 'password123');
if ($user) {
    echo "Welcome, " . $user['username'];
}

// Check authentication
$current_user = users::checkAuth();
if ($current_user) {
    echo "User is logged in: " . $current_user['email'];
}
```

---

### data_importer Class

**Namespace:** `system`  
**File:** `system/classes/data_importer.class.php`

A general-purpose utility class for importing data from CSV and JSON files into database tables.

#### Properties

```php
public static string $log_target = "data_importer";  // Logging target
```

#### Key Methods

##### `import_csv_to_hilt_table($table_name, $csv_data_or_path, $is_file_path, $replace_all)`
Imports CSV data directly into a hilt table.

```php
public static function import_csv_to_hilt_table(
    string $table_name, 
    string $csv_data_or_path, 
    bool $is_file_path = true, 
    bool $replace_all = false
): array
```

**Parameters:**
- `$table_name` - Database table name
- `$csv_data_or_path` - Path to CSV file or CSV data string
- `$is_file_path` - Whether the second parameter is a file path
- `$replace_all` - Whether to clear existing data first

**Returns:** Import result array with status and statistics

##### `import_data_into_database($mapping, $file_path, $file_type, $unique_hash, $debug)`
Imports data into database tables based on mapping configuration.

```php
public static function import_data_into_database(
    array $mapping, 
    string $file_path, 
    string $file_type = 'csv', 
    array $unique_hash = [], 
    bool $debug = true, 
    string $log_target = "main"
): array
```

**Parameters:**
- `$mapping` - Mapping configuration for database tables and columns
- `$file_path` - Path to the data file (CSV or JSON)
- `$file_type` - Type of file ('csv' or 'json')
- `$unique_hash` - Headers/keys to use for generating unique hashes
- `$debug` - Whether to return detailed debug information

**Features:**
- Supports both CSV and JSON data sources
- Configurable column mapping
- Duplicate detection using unique hashes
- Batch processing for large datasets
- Comprehensive error handling and logging

**Usage Example:**
```php
// Import CSV data to hilt table
$result = data_importer::import_csv_to_hilt_table(
    'autobooks_products_data',
    '/path/to/products.csv',
    true,
    false
);

if ($result['status'] === 'success') {
    echo "Imported {$result['imported_count']} records";
}

// Import with custom mapping
$mapping = [
    'products' => [
        'table' => 'products',
        'key' => 'product_id',
        'columns' => [
            'name' => 'product_name',
            'price' => 'product_price'
        ]
    ]
];

$result = data_importer::import_data_into_database(
    $mapping,
    '/path/to/data.csv',
    'csv',
    ['name', 'sku']
);
```
